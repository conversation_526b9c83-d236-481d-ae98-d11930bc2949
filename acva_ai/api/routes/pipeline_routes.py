import logging
import os
import uuid
from typing import List, Optional

from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    File,
    HTTPException,
    Query,
    UploadFile,
)
from fastapi.responses import JSONResponse

from acva_ai.database import mongo_instance
from acva_ai.models import Task
from acva_ai.pipeline.main import process_visit_background
from acva_ai.utils.audio_utils import concatenate_audio_files, normalize_audio_segment
from acva_ai.utils.security_service import SecurityService
from acva_ai.llm.llm_orchestrator import LL<PERSON>rovider
from acva_ai.llm.transcript_orchestrator import TranscriptProvider
from acva_ai._params import DEFAULT_TRANSCRIPT_PROVIDER

logger = logging.getLogger(__name__)

# Define a constant for the data directory
DATA_DIR = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "data"
)
os.makedirs(DATA_DIR, exist_ok=True)

pipeline_router = APIRouter(dependencies=[Depends(SecurityService.get_api_key)])


@pipeline_router.post("/process-visit", tags=["Pipeline"])
async def process_visit_endpoint(
    files: List[UploadFile] = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    provider: Optional[str] = Query(
        None, description="LLM provider to use (azure, openai, lazarus)"
    ),
    transcript_provider: TranscriptProvider = Query(
        TranscriptProvider(DEFAULT_TRANSCRIPT_PROVIDER),
        description="Transcript provider to use (azure, elevenlabs)",
    ),
):
    """
    Process a complete medical visit from audio files.

    This endpoint handles the complete pipeline audio_processing:
    1. Receives audio files (typically from browser recordings)
    2. Concatenates multiple files if provided
    3. Converts to WAV format for audio_processing
    4. Processes the audio through the medical transcription pipeline

    Args:
        files: Audio files to process (WebM, MP3, WAV, M4A formats supported)
        background_tasks: FastAPI BackgroundTasks for non-blocking audio_processing
        provider: LLM provider to use (azure, openai, lazarus)

    Returns:
        JSON response with task_id for tracking the audio_processing status
    """
    try:
        # Create a unique task ID
        task_uuid = uuid.uuid4()
        task_id = str(task_uuid)

        # Parse LLM provider enum
        llm_provider = LLMProvider.OPENAI  # Default provider
        if provider:
            try:
                llm_provider = LLMProvider(provider.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid provider: {provider}. Must be one of: {', '.join([p.value for p in LLMProvider])}",
                )

        # Create a task entry in the database
        task = Task(
            task_id=task_uuid,
            status="queued",
            metadata={
                "llm_provider": llm_provider.value,
                "transcript_provider": transcript_provider.value,
            },
        ).model_dump()
        mongo_instance.create_task(task)

        # Instead of concatenating and saving, directly process the list of files
        audio_segment = concatenate_audio_files(files)
        audio_segment = normalize_audio_segment(audio_segment)

        background_tasks.add_task(
            process_visit_background,
            task_id,
            audio_segment,
            transcript_provider,
            llm_provider,
        )

        return JSONResponse(
            {
                "task_id": task_id,
                "status": "queued",
                "llm_provider": llm_provider.value,
                "transcript_provider": transcript_provider.value,
            }
        )
    except Exception as e:
        logger.error("Error in process_visit_endpoint: %s", e)
        raise HTTPException(status_code=500, detail="Internal server error")
